/**
 * Canvas State Protector
 * Safety net that detects canvas clearing and restores canvas state automatically
 * Part of Phase 2: Canvas State Protection for Royal Portrait Builder canvas clearing regression fix
 */

console.log('🛡️ Canvas State Protector Loading...');

/**
 * Canvas State Protector Class
 * Monitors canvas state and provides automatic restoration when clearing is detected
 */
class CanvasStateProtector {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.monitoringInterval = options.interval || 100; // 100ms monitoring
    this.backupInterval = options.backupInterval || 500; // 500ms backup
    this.layerBackup = null;
    this.isProtectionEnabled = true;
    this.clearingDetected = false;

    // Enhanced performance metrics (Task 2.2)
    this.performanceMetrics = {
      monitoringTime: 0,
      backupTime: 0,
      restorationTime: 0,
      monitoringCount: 0,
      backupCount: 0,
      restorationCount: 0,
      memoryUsage: 0,
      adaptiveAdjustments: 0,
      frameSelectionImpact: 0,
    };

    // Adaptive monitoring configuration (Task 2.2)
    this.adaptiveConfig = {
      baseMonitoringInterval: this.monitoringInterval,
      currentMonitoringInterval: this.monitoringInterval,
      maxMonitoringInterval: 500, // Slow down to 500ms when idle
      minMonitoringInterval: 50, // Speed up to 50ms during activity
      userInteractionDetected: false,
      lastInteractionTime: Date.now(),
      idleThreshold: 5000, // 5 seconds of inactivity
      performanceThreshold: 10, // Switch to adaptive if avg monitoring > 10ms
    };

    // Memory monitoring (Task 2.2)
    this.memoryMonitor = {
      initialMemory: this.getMemoryUsage(),
      peakMemory: 0,
      lastMemoryCheck: Date.now(),
    };

    console.log('🛡️ Canvas State Protector initialized with performance optimization', {
      monitoringInterval: this.monitoringInterval,
      backupInterval: this.backupInterval,
      adaptiveMonitoring: true,
      canvas: this.canvas,
    });

    this.initializeUserInteractionDetection();
    this.startMonitoring();
  }

  /**
   * Initialize user interaction detection for adaptive monitoring (Task 2.2)
   */
  initializeUserInteractionDetection() {
    const interactionEvents = ['click', 'mousemove', 'keydown', 'touchstart', 'scroll'];

    this.interactionHandler = () => {
      this.adaptiveConfig.userInteractionDetected = true;
      this.adaptiveConfig.lastInteractionTime = Date.now();
      this.adjustMonitoringFrequency();
    };

    // Listen for user interactions on the canvas container
    if (this.canvas.canvas && this.canvas.canvas.parentElement) {
      interactionEvents.forEach((event) => {
        this.canvas.canvas.parentElement.addEventListener(event, this.interactionHandler, { passive: true });
      });
    }

    console.log('🎯 User interaction detection initialized for adaptive monitoring');
  }

  /**
   * Adjust monitoring frequency based on user activity (Task 2.2)
   */
  adjustMonitoringFrequency() {
    const now = Date.now();
    const timeSinceLastInteraction = now - this.adaptiveConfig.lastInteractionTime;
    const isIdle = timeSinceLastInteraction > this.adaptiveConfig.idleThreshold;

    let newInterval = this.adaptiveConfig.baseMonitoringInterval;

    if (this.adaptiveConfig.userInteractionDetected && !isIdle) {
      // User is active - increase monitoring frequency
      newInterval = this.adaptiveConfig.minMonitoringInterval;
    } else if (isIdle) {
      // User is idle - decrease monitoring frequency
      newInterval = this.adaptiveConfig.maxMonitoringInterval;
    }

    if (newInterval !== this.adaptiveConfig.currentMonitoringInterval) {
      this.adaptiveConfig.currentMonitoringInterval = newInterval;
      this.adaptiveConfig.adaptiveAdjustments++;

      // Restart monitoring timer with new interval
      if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer);
        this.startMonitoringTimer();
      }

      console.log(`🔄 Adaptive monitoring: ${newInterval}ms (${isIdle ? 'idle' : 'active'})`);
    }
  }

  /**
   * Start monitoring and backup timers with adaptive frequency (Task 2.2)
   */
  startMonitoring() {
    console.log('🛡️ Starting adaptive canvas state monitoring...');

    this.startMonitoringTimer();
    this.startBackupTimer();
    this.startPerformanceMonitoring();
  }

  /**
   * Start monitoring timer with current adaptive interval
   */
  startMonitoringTimer() {
    this.monitoringTimer = setInterval(() => {
      if (this.isProtectionEnabled) {
        this.checkCanvasState();
        this.adjustMonitoringFrequency();
      }
    }, this.adaptiveConfig.currentMonitoringInterval);
  }

  /**
   * Start backup timer
   */
  startBackupTimer() {
    this.backupTimer = setInterval(() => {
      if (this.isProtectionEnabled && this.hasImportantLayers()) {
        this.backupCanvasState();
      }
    }, this.backupInterval);
  }

  /**
   * Start performance monitoring timer (Task 2.2)
   */
  startPerformanceMonitoring() {
    this.performanceTimer = setInterval(() => {
      this.updateMemoryMetrics();
      this.evaluatePerformanceThresholds();
    }, 2000); // Check every 2 seconds
  }

  /**
   * Update memory usage metrics (Task 2.2)
   */
  updateMemoryMetrics() {
    try {
      const currentMemory = this.getMemoryUsage();
      this.performanceMetrics.memoryUsage = currentMemory - this.memoryMonitor.initialMemory;

      if (currentMemory > this.memoryMonitor.peakMemory) {
        this.memoryMonitor.peakMemory = currentMemory;
      }

      this.memoryMonitor.lastMemoryCheck = Date.now();
    } catch (error) {
      console.warn('🛡️ Memory monitoring not available:', error.message);
    }
  }

  /**
   * Get current memory usage (Task 2.2)
   */
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0; // Fallback if memory API not available
  }

  /**
   * Evaluate performance thresholds and adjust behavior (Task 2.2)
   */
  evaluatePerformanceThresholds() {
    const avgMonitoringTime =
      this.performanceMetrics.monitoringCount > 0
        ? this.performanceMetrics.monitoringTime / this.performanceMetrics.monitoringCount
        : 0;

    // If monitoring is taking too long, reduce frequency
    if (avgMonitoringTime > this.adaptiveConfig.performanceThreshold) {
      this.adaptiveConfig.maxMonitoringInterval = Math.min(1000, this.adaptiveConfig.maxMonitoringInterval * 1.2);
      console.log(
        `⚡ Performance adjustment: increased max interval to ${this.adaptiveConfig.maxMonitoringInterval}ms`
      );
    }

    // Memory pressure detection
    const memoryUsageMB = this.performanceMetrics.memoryUsage / (1024 * 1024);
    if (memoryUsageMB > 10) {
      // More than 10MB additional usage
      console.warn(`🧠 High memory usage detected: ${memoryUsageMB.toFixed(2)}MB`);
    }
  }

  /**
   * Measure frame selection impact (Task 2.2)
   */
  measureFrameSelectionImpact(callback) {
    const startTime = performance.now();

    const result = callback();

    const impactTime = performance.now() - startTime;
    this.performanceMetrics.frameSelectionImpact = Math.max(this.performanceMetrics.frameSelectionImpact, impactTime);

    if (impactTime > 50) {
      console.warn(`⚠️ Frame selection impact exceeded 50ms: ${impactTime.toFixed(2)}ms`);
    }

    return result;
  }

  /**
   * Check canvas state for clearing detection with enhanced performance tracking (Task 2.2)
   */
  checkCanvasState() {
    const startTime = performance.now();

    try {
      const hasLayers = this.hasImportantLayers();
      const isCanvasCleared = this.isCanvasCleared();

      // Detect clearing: we had layers backed up, but now canvas is cleared and no layers exist
      if (!hasLayers && this.layerBackup && isCanvasCleared && !this.clearingDetected) {
        console.warn('🛡️ Canvas clearing detected, initiating restoration');
        this.clearingDetected = true;
        this.restoreCanvasState();
      } else if (hasLayers && this.clearingDetected) {
        // Reset clearing detection flag when layers are restored
        this.clearingDetected = false;
        console.log('🛡️ Canvas state normalized, clearing detection reset');
      }

      // Enhanced performance metrics (Task 2.2)
      const monitoringTime = performance.now() - startTime;
      this.performanceMetrics.monitoringTime += monitoringTime;
      this.performanceMetrics.monitoringCount++;

      // Log performance warnings if monitoring is taking too long
      if (monitoringTime > 5) {
        console.warn(`⚡ Slow monitoring detected: ${monitoringTime.toFixed(2)}ms`);
      }
    } catch (error) {
      console.error('🛡️ Error during canvas state check:', error);
    }
  }

  /**
   * Check if canvas has important layers that should be preserved
   * @returns {boolean} True if canvas has important layers
   */
  hasImportantLayers() {
    return (
      this.canvas.layers &&
      (this.canvas.layers.breed ||
        this.canvas.layers.costume ||
        this.canvas.layers.frame ||
        this.canvas.layers.background)
    );
  }

  /**
   * Check if canvas appears to be cleared (mostly white/transparent)
   * @returns {boolean} True if canvas appears cleared
   */
  isCanvasCleared() {
    if (!this.canvas.canvas) return true;

    try {
      const ctx = this.canvas.canvas.getContext('2d');
      const imageData = ctx.getImageData(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
      const data = imageData.data;

      // Check if canvas is mostly white/transparent
      let nonWhitePixels = 0;
      const totalPixels = data.length / 4;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];

        // Count pixels that are not white/transparent
        if (!(r > 250 && g > 250 && b > 250) && a > 0) {
          nonWhitePixels++;
        }
      }

      // Canvas is considered cleared if less than 1% of pixels are non-white
      return nonWhitePixels < totalPixels * 0.01;
    } catch (error) {
      console.error('🛡️ Error checking canvas state:', error);
      return false;
    }
  }

  /**
   * Backup current canvas state
   */
  backupCanvasState() {
    const startTime = performance.now();

    try {
      this.layerBackup = {
        layers: JSON.parse(JSON.stringify(this.canvas.layers)),
        canvasData: this.canvas.canvas.toDataURL(),
        timestamp: Date.now(),
      };

      console.log('💾 Canvas state backed up', {
        layers: Object.keys(this.layerBackup.layers).filter((key) => this.layerBackup.layers[key]),
        timestamp: this.layerBackup.timestamp,
      });

      // Update performance metrics
      this.performanceMetrics.backupTime += performance.now() - startTime;
      this.performanceMetrics.backupCount++;
    } catch (error) {
      console.error('🛡️ Error backing up canvas state:', error);
    }
  }

  /**
   * Restore canvas state from backup
   */
  async restoreCanvasState() {
    if (!this.layerBackup) {
      console.warn('🛡️ No backup available for restoration');
      return;
    }

    const startTime = performance.now();

    try {
      console.log('🔄 Restoring canvas state from backup');

      // Restore layer state
      this.canvas.layers = { ...this.layerBackup.layers };

      // Restore visual content
      const img = new Image();
      img.onload = () => {
        const ctx = this.canvas.canvas.getContext('2d');
        ctx.clearRect(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
        ctx.drawImage(img, 0, 0);
        console.log('✅ Canvas state restored successfully');

        // Update performance metrics
        this.performanceMetrics.restorationTime += performance.now() - startTime;
        this.performanceMetrics.restorationCount++;

        // Reset clearing detection flag after successful restoration
        setTimeout(() => {
          this.clearingDetected = false;
        }, 200);
      };

      img.onerror = (error) => {
        console.error('❌ Failed to load backup image for restoration:', error);
        this.clearingDetected = false;
      };

      img.src = this.layerBackup.canvasData;
    } catch (error) {
      console.error('❌ Failed to restore canvas state:', error);
      this.clearingDetected = false;
    }
  }

  /**
   * Get enhanced performance metrics (Task 2.2)
   * @returns {Object} Comprehensive performance metrics
   */
  getPerformanceMetrics() {
    const memoryUsageMB = this.performanceMetrics.memoryUsage / (1024 * 1024);

    return {
      ...this.performanceMetrics,
      // Basic averages
      averageMonitoringTime:
        this.performanceMetrics.monitoringCount > 0
          ? this.performanceMetrics.monitoringTime / this.performanceMetrics.monitoringCount
          : 0,
      averageBackupTime:
        this.performanceMetrics.backupCount > 0
          ? this.performanceMetrics.backupTime / this.performanceMetrics.backupCount
          : 0,
      averageRestorationTime:
        this.performanceMetrics.restorationCount > 0
          ? this.performanceMetrics.restorationTime / this.performanceMetrics.restorationCount
          : 0,

      // Task 2.2 enhanced metrics
      memoryUsageMB: memoryUsageMB,
      peakMemoryMB: this.memoryMonitor.peakMemory / (1024 * 1024),
      currentMonitoringInterval: this.adaptiveConfig.currentMonitoringInterval,
      adaptiveAdjustments: this.adaptiveConfig.adaptiveAdjustments,
      frameSelectionImpact: this.performanceMetrics.frameSelectionImpact,

      // Performance status
      isPerformanceOptimal: this.isPerformanceOptimal(),
      performanceGrade: this.getPerformanceGrade(),

      // Adaptive monitoring status
      adaptiveStatus: {
        isIdle: Date.now() - this.adaptiveConfig.lastInteractionTime > this.adaptiveConfig.idleThreshold,
        currentInterval: this.adaptiveConfig.currentMonitoringInterval,
        baseInterval: this.adaptiveConfig.baseMonitoringInterval,
        adjustmentCount: this.adaptiveConfig.adaptiveAdjustments,
      },
    };
  }

  /**
   * Check if performance is within optimal thresholds (Task 2.2)
   */
  isPerformanceOptimal() {
    const avgMonitoring =
      this.performanceMetrics.monitoringCount > 0
        ? this.performanceMetrics.monitoringTime / this.performanceMetrics.monitoringCount
        : 0;

    return (
      avgMonitoring < 5 &&
      this.performanceMetrics.frameSelectionImpact < 50 &&
      this.performanceMetrics.memoryUsage < 10 * 1024 * 1024
    ); // 10MB
  }

  /**
   * Get performance grade (Task 2.2)
   */
  getPerformanceGrade() {
    const avgMonitoring =
      this.performanceMetrics.monitoringCount > 0
        ? this.performanceMetrics.monitoringTime / this.performanceMetrics.monitoringCount
        : 0;

    if (avgMonitoring < 2 && this.performanceMetrics.frameSelectionImpact < 20) {
      return 'A'; // Excellent
    } else if (avgMonitoring < 5 && this.performanceMetrics.frameSelectionImpact < 50) {
      return 'B'; // Good
    } else if (avgMonitoring < 10) {
      return 'C'; // Acceptable
    } else {
      return 'D'; // Needs optimization
    }
  }

  /**
   * Enable protection
   */
  enable() {
    this.isProtectionEnabled = true;
    console.log('🛡️ Canvas protection enabled');
  }

  /**
   * Disable protection
   */
  disable() {
    this.isProtectionEnabled = false;
    console.log('🛡️ Canvas protection disabled');
  }

  /**
   * Destroy protector and clean up all resources (Task 2.2 enhanced)
   */
  destroy() {
    // Clean up all timers
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }

    if (this.backupTimer) {
      clearInterval(this.backupTimer);
      this.backupTimer = null;
    }

    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;
    }

    // Clean up event listeners (Task 2.2)
    if (this.interactionHandler && this.canvas.canvas && this.canvas.canvas.parentElement) {
      const interactionEvents = ['click', 'mousemove', 'keydown', 'touchstart', 'scroll'];
      interactionEvents.forEach((event) => {
        this.canvas.canvas.parentElement.removeEventListener(event, this.interactionHandler);
      });
    }

    this.isProtectionEnabled = false;

    const finalMetrics = this.getPerformanceMetrics();
    console.log('🛡️ Canvas State Protector destroyed with final performance report', {
      performanceMetrics: finalMetrics,
      performanceGrade: finalMetrics.performanceGrade,
      totalAdaptiveAdjustments: finalMetrics.adaptiveAdjustments,
      memoryUsage: `${finalMetrics.memoryUsageMB.toFixed(2)}MB`,
    });
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { CanvasStateProtector };
} else {
  window.CanvasStateProtector = CanvasStateProtector;
}

console.log('🛡️ Canvas State Protector loaded successfully');
